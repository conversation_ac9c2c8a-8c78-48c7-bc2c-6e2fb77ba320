-- oracle 数据库 ，表 DM_YS_DAILY_DETAIL
-- 列信息：
-- ZQSQ_VALUE	滞期速遣费
-- TRANSPORTTYPE_CODE	运输方式改变code
-- TRANSPORTTYPE_NAME	运输方式改变name
-- PK_TRANSPRICE	运价主表id
-- PK_TRANSPRICE_B	运价子表newid
-- MARBASCLASS_CODE_HG	化工物料编码
-- MARBASCLASS_NAME_HG	化工物料名称
-- VWEIGHCODE	磅单号
-- YSFS_NAME	运输方式名称
-- RECEIPT_DELIVERY_AREA_CODE	收发货区域编码
-- RECEIPT_DELIVERY_AREA	收发货区域名称
-- PK_CUSTADDRESS	实际运输地点ID
-- IS_RISUN	是否旭阳承运
-- LAST_CUSTOMERID	实际客户id
-- LAST_CUST_CODE	实际客户(客商)编码
-- LAST_CUST_NAME	实际客户(客商)名称
-- JSFS	结算方式
-- YSJL_VALUE	运输距离
-- YJ_VALUE	运价
-- YL_VALUE	运量
-- YF_VALUE	运费
-- CC_VALUE	车次
-- FCL_VALUE	发出量
-- YWL_VALUE	业务量
-- FBL_VALUE	复磅量
-- ETL_USER	数据更新人
-- ETL_DATE	数据更新时间
-- DATA_SOURCE	数据来源
-- BZ	备注
-- CUSTADDRESS_CODE	实际运输地点编码
-- CUSTADDRESS_NAME	实际运输地点名称
-- PK_M_TYPE	上级物料分类
-- M_TYPE_CODE	上级物料分类编码
-- M_TYPE_NAME	上级物料分类名称
-- VBILLCODE	单据号
-- ORG_CODE	公司编码
-- ORG_NAME	公司名称
-- ORG_SHORTNAME	公司简称
-- PERIOD_ID	日期
-- PLATE_CODE	板块编码
-- PLATE_NAME	板块名称
-- CPX_CODE	产品线编码
-- CPX_NAME	产品线名称
-- INDEX_CODE	指标编码
-- INDEX_NAME	指标名称
-- SCENE_CODE	情景编码（预算/实际）
-- SCENE_NAME	情景名称（预算/实际）
-- DICT_CODE	购销类型编码（供应/销售）
-- DICT_NAME	购销类型名称（供应/销售）
-- MARBASCLASS_CODE	物料编码
-- MARBASCLASS_NAME	物料名称
-- PRODUCTID	产品id
-- CYS_ID	承运商id
-- CYS_CODE	承运商编码
-- CYS_NAME	承运商名称
-- CUSTOMERID	客户id
-- CUST_CODE	客户(客商)编码
-- CUST_NAME	客户(客商)名称
-- YSFS_CODE	运输方式编码


更新语句：

-- 更新DM_YS_DAILY_DETAIL表中YSJL_VALUE字段
-- 根据指定维度组合，取最新日期的YSJL_VALUE值进行更新
-- 只更新在相同维度下YSJL_VALUE有多个不同值的记录

UPDATE DM_YS_DAILY_DETAIL d1
SET YSJL_VALUE = (
    SELECT YSJL_VALUE
    FROM (
        SELECT
            ORG_CODE,
            INDEX_CODE,
            SCENE_CODE,
            DICT_CODE,
            MARBASCLASS_CODE,
            CUST_CODE,
            YSFS_CODE,
            RECEIPT_DELIVERY_AREA_CODE,
            LAST_CUST_CODE,
            YSJL_VALUE,
            ROW_NUMBER() OVER (
                PARTITION BY
                    ORG_CODE,
                    INDEX_CODE,
                    SCENE_CODE,
                    DICT_CODE,
                    MARBASCLASS_CODE,
                    CUST_CODE,
                    YSFS_CODE,
                    RECEIPT_DELIVERY_AREA_CODE,
                    LAST_CUST_CODE
                ORDER BY PERIOD_ID DESC
            ) as rn
        FROM DM_YS_DAILY_DETAIL
        WHERE ORG_CODE = d1.ORG_CODE
          AND INDEX_CODE = d1.INDEX_CODE
          AND SCENE_CODE = d1.SCENE_CODE
          AND DICT_CODE = d1.DICT_CODE
          AND MARBASCLASS_CODE = d1.MARBASCLASS_CODE
          AND CUST_CODE = d1.CUST_CODE
          AND YSFS_CODE = d1.YSFS_CODE
          AND RECEIPT_DELIVERY_AREA_CODE = d1.RECEIPT_DELIVERY_AREA_CODE
          AND LAST_CUST_CODE = d1.LAST_CUST_CODE
    ) latest
    WHERE latest.rn = 1
),
ETL_DATE = SYSDATE,
ETL_USER = 'SYSTEM'
WHERE EXISTS (
    -- 只更新在相同维度下YSJL_VALUE有多个不同值的记录
    SELECT 1
    FROM (
        SELECT
            ORG_CODE,
            INDEX_CODE,
            SCENE_CODE,
            DICT_CODE,
            MARBASCLASS_CODE,
            CUST_CODE,
            YSFS_CODE,
            RECEIPT_DELIVERY_AREA_CODE,
            LAST_CUST_CODE,
            COUNT(DISTINCT YSJL_VALUE) as distinct_count
        FROM DM_YS_DAILY_DETAIL
        GROUP BY
            ORG_CODE,
            INDEX_CODE,
            SCENE_CODE,
            DICT_CODE,
            MARBASCLASS_CODE,
            CUST_CODE,
            YSFS_CODE,
            RECEIPT_DELIVERY_AREA_CODE,
            LAST_CUST_CODE
        HAVING COUNT(DISTINCT YSJL_VALUE) > 1
    ) multi_values
    WHERE multi_values.ORG_CODE = d1.ORG_CODE
      AND multi_values.INDEX_CODE = d1.INDEX_CODE
      AND multi_values.SCENE_CODE = d1.SCENE_CODE
      AND multi_values.DICT_CODE = d1.DICT_CODE
      AND multi_values.MARBASCLASS_CODE = d1.MARBASCLASS_CODE
      AND multi_values.CUST_CODE = d1.CUST_CODE
      AND multi_values.YSFS_CODE = d1.YSFS_CODE
      AND multi_values.RECEIPT_DELIVERY_AREA_CODE = d1.RECEIPT_DELIVERY_AREA_CODE
      AND multi_values.LAST_CUST_CODE = d1.LAST_CUST_CODE
);

-- 提交事务
COMMIT;

-- 查询验证更新结果（可选）
-- 检查是否还有相同维度下YSJL_VALUE不一致的情况
SELECT
    ORG_CODE,
    INDEX_CODE,
    SCENE_CODE,
    DICT_CODE,
    MARBASCLASS_CODE,
    CUST_CODE,
    YSFS_CODE,
    RECEIPT_DELIVERY_AREA_CODE,
    LAST_CUST_CODE,
    COUNT(DISTINCT YSJL_VALUE) as distinct_ysjl_count,
    COUNT(*) as total_records
FROM DM_YS_DAILY_DETAIL
GROUP BY
    ORG_CODE,
    INDEX_CODE,
    SCENE_CODE,
    DICT_CODE,
    MARBASCLASS_CODE,
    CUST_CODE,
    YSFS_CODE,
    RECEIPT_DELIVERY_AREA_CODE,
    LAST_CUST_CODE
HAVING COUNT(DISTINCT YSJL_VALUE) > 1
ORDER BY distinct_ysjl_count DESC;
