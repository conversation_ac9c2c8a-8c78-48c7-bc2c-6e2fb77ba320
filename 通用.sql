-- oracle 数据库 ，表 DM_YS_DAILY_DETAIL
-- 列信息：
-- ZQSQ_VALUE	滞期速遣费
-- TRANSPORTTYPE_CODE	运输方式改变code
-- TRANSPORTTYPE_NAME	运输方式改变name
-- PK_TRANSPRICE	运价主表id
-- PK_TRANSPRICE_B	运价子表newid
-- MARBASCLASS_CODE_HG	化工物料编码
-- MARBASCLASS_NAME_HG	化工物料名称
-- VWEIGHCODE	磅单号
-- YSFS_NAME	运输方式名称
-- RECEIPT_DELIVERY_AREA_CODE	收发货区域编码
-- RECEIPT_DELIVERY_AREA	收发货区域名称
-- PK_CUSTADDRESS	实际运输地点ID
-- IS_RISUN	是否旭阳承运
-- LAST_CUSTOMERID	实际客户id
-- LAST_CUST_CODE	实际客户(客商)编码
-- LAST_CUST_NAME	实际客户(客商)名称
-- JSFS	结算方式
-- YSJL_VALUE	运输距离
-- YJ_VALUE	运价
-- YL_VALUE	运量
-- YF_VALUE	运费
-- CC_VALUE	车次
-- FCL_VALUE	发出量
-- YWL_VALUE	业务量
-- FBL_VALUE	复磅量
-- ETL_USER	数据更新人
-- ETL_DATE	数据更新时间
-- DATA_SOURCE	数据来源
-- BZ	备注
-- CUSTADDRESS_CODE	实际运输地点编码
-- CUSTADDRESS_NAME	实际运输地点名称
-- PK_M_TYPE	上级物料分类
-- M_TYPE_CODE	上级物料分类编码
-- M_TYPE_NAME	上级物料分类名称
-- VBILLCODE	单据号
-- ORG_CODE	公司编码
-- ORG_NAME	公司名称
-- ORG_SHORTNAME	公司简称
-- PERIOD_ID	日期
-- PLATE_CODE	板块编码
-- PLATE_NAME	板块名称
-- CPX_CODE	产品线编码
-- CPX_NAME	产品线名称
-- INDEX_CODE	指标编码
-- INDEX_NAME	指标名称
-- SCENE_CODE	情景编码（预算/实际）
-- SCENE_NAME	情景名称（预算/实际）
-- DICT_CODE	购销类型编码（供应/销售）
-- DICT_NAME	购销类型名称（供应/销售）
-- MARBASCLASS_CODE	物料编码
-- MARBASCLASS_NAME	物料名称
-- PRODUCTID	产品id
-- CYS_ID	承运商id
-- CYS_CODE	承运商编码
-- CYS_NAME	承运商名称
-- CUSTOMERID	客户id
-- CUST_CODE	客户(客商)编码
-- CUST_NAME	客户(客商)名称
-- YSFS_CODE	运输方式编码



-- 现在我想更新一下这个表里的数据，就是在 org_code,
--                index_code,
--                scene_code,
--                dict_code,
--                marbasclass_code,
--                cust_code,
--                ysfs_code,
--                receipt_delivery_area_code,
--                last_cust_code
--            这几个维度下，理论上     ysjl_value  应该是一样的，但是现在有不同的情况，现在就是想根据这些维度去更新这个        ysjl_value
--            取数就取这些维度下，然后看最新日期（period_id 格式为 yyyymmdd）的 那个值，更新到DM_YS_DAILY_DETAIL表里，为了提高效率，可以只更新 ysjl_value 在这些维度下有多个值的那些数据。
