-- 创建测试表
CREATE TABLE IF NOT EXISTS test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INT,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些测试数据
INSERT INTO test_table (name, age, email) VALUES
    ('张三', 25, 'zhang<PERSON>@example.com'),
    ('李四', 30, '<EMAIL>'),
    ('王五', 28, '<EMAIL>'),
    ('赵六', 35, 'zhao<PERSON><PERSON>@example.com'),
    ('钱七', 22, '<EMAIL>');

-- 查询测试数据
SELECT * FROM test_table;